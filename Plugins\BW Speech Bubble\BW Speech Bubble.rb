#-------------------------------------------------------------------------------
# BW Speech Bubbles for v21
# Updated by NoNonever
# Enhanced with new animation system and bubbleskinnew skin
#-------------------------------------------------------------------------------
# To use, call pbCallBub(type, eventID)
#
# Where type is either 1 or 2:
# 1 - floating bubble (with smooth slide and fade animation)
# 2 - speech bubble with arrow (positioned above/below event)
#
# New Features:
# - Uses "bubbleskinnew" as default skin
# - Smooth slide and fade animations
# - Arrow_Down and Arrow_Up images based on event position
# - 45 character text limit per bubble
# - Smart positioning relative to event location
#
# Note: Call pbUpdateSpeechBubbleAnimation(msgwindow) in your message loop
# to enable smooth animations
#-------------------------------------------------------------------------------

#-------------------------------------------------------------------------------
# Class modifiers
#-------------------------------------------------------------------------------

class Game_Temp
  attr_accessor :speechbubble_bubble
  attr_accessor :speechbubble_vp
  attr_accessor :speechbubble_arrow
  attr_accessor :speechbubble_outofrange
  attr_accessor :speechbubble_talking
  attr_accessor :speechbubble_animating
  attr_accessor :speechbubble_target_y
  attr_accessor :speechbubble_arrow_direction
end

module MessageConfig
  BUBBLETEXTBASE  = Color.new(248,248,248)
  BUBBLETEXTSHADOW= Color.new(72,80,88)
end

#-------------------------------------------------------------------------------
# Function modifiers
#-------------------------------------------------------------------------------

class Window_AdvancedTextPokemon
  def text=(value)
    if value != nil && value != "" && $game_temp.speechbubble_bubble && $game_temp.speechbubble_bubble > 0
      if $game_temp.speechbubble_bubble == 1
        $game_temp.speechbubble_bubble = 0
        # Limit text to 45 characters for speech bubbles
        limited_text = limitTextLength(value, 45)
        resizeToFit2(limited_text, 400, 96)
        # Use player if speaking, otherwise the event
        if $game_temp.speechbubble_talking == -1
          speaker_x = $game_player.screen_x
          speaker_y = $game_player.screen_y
        else
          speaker = $game_map.events[$game_temp.speechbubble_talking]
          speaker_x = speaker.screen_x
          speaker_y = speaker.screen_y
        end
        
        # Position bubble above or below event based on screen position
        @x = speaker_x - (@width / 2)  # Center bubble horizontally on the event
        if speaker_y < Graphics.height / 2
          # Event in upper half, show bubble below
          @y = speaker_y + 40  # Increased gap for smaller bubble
          $game_temp.speechbubble_arrow_direction = "down"
        else
          # Event in lower half, show bubble above
          @y = speaker_y - (40 + @height)  # Increased gap for smaller bubble
          $game_temp.speechbubble_arrow_direction = "up"
        end
        
        # Keep bubble on screen
        if @y > (Graphics.height - @height - 2)
          @y = (Graphics.height - @height)
        elsif @y < 2
          @y = 2
        end
        if @x > (Graphics.width - @width - 2)
          @x = (Graphics.width - @width - 2)
        elsif @x < 2
          @x = 2
        end
        
        # Store target position and start animation
        $game_temp.speechbubble_target_y = @y
        $game_temp.speechbubble_animating = true
        
        # Start bubble off-screen for animation
        if $game_temp.speechbubble_arrow_direction == "down"
          @y = $game_temp.speechbubble_target_y - 30  # Reduced offset for smaller bubble
        else
          @y = $game_temp.speechbubble_target_y + 30  # Reduced offset for smaller bubble
        end
        
        self.opacity = 0
      else
        $game_temp.speechbubble_bubble = 0
      end
    end
    setText(value)
  end
  
  def limitTextLength(text, max_chars)
    if text.length > max_chars
      # Find the last space before the limit to avoid breaking words
      break_point = text.rindex(' ', max_chars) || max_chars
      return text[0, break_point] + "..."
    end
    return text
  end
end 

def pbRepositionMessageWindow(msgwindow, linecount=2)
  msgwindow.height=32*linecount+msgwindow.borderY
  msgwindow.y=(Graphics.height)-(msgwindow.height)
  if $game_temp && $game_temp.in_battle && !$scene.respond_to?("update_basic")
    msgwindow.y=0
  elsif $game_system && $game_system.respond_to?("message_position")
    case $game_system.message_position
    when 0  # up
      msgwindow.y=0
    when 1  # middle
      msgwindow.y=(Graphics.height/2)-(msgwindow.height/2)
    when 2
      if $game_temp.speechbubble_bubble==1
       msgwindow.setSkin("Graphics/windowskins/bubbleskinnew")
       msgwindow.height = 96
       msgwindow.width = 400
     elsif $game_temp.speechbubble_bubble==2
       msgwindow.setSkin("Graphics/windowskins/bubbleskinnew")
       msgwindow.height = 96
       msgwindow.width = 400
       # Center the bubble on the speaking event
       if $game_temp.speechbubble_talking == -1
         speaker_x = $game_player.screen_x
         speaker_y = $game_player.screen_y
       else
         speaker = $game_map.events[$game_temp.speechbubble_talking]
         speaker_x = speaker.screen_x
         speaker_y = speaker.screen_y
       end
       
       msgwindow.x = speaker_x - (msgwindow.width / 2)  # Center horizontally on event
       if speaker_y < Graphics.height / 2
         # Event in upper half, show bubble Above
         msgwindow.y = speaker_y - 168
       else
         # Event in lower half, show bubble below
         msgwindow.y = speaker_y + 16
       end
       
       # Keep bubble on screen
       if msgwindow.x < 0
         msgwindow.x = 0
       elsif msgwindow.x > Graphics.width - msgwindow.width
         msgwindow.x = Graphics.width - msgwindow.width
       end
       if msgwindow.y < 0
         msgwindow.y = 0
       elsif msgwindow.y > Graphics.height - msgwindow.height
         msgwindow.y = Graphics.height - msgwindow.height
       end
      else
        msgwindow.height = 102
        msgwindow.y = Graphics.height - msgwindow.height - 6
      end
    end
  end
  if $game_system && $game_system.respond_to?("message_frame")
    if $game_system.message_frame != 0
      msgwindow.opacity = 0
    end
  end
  if $game_message
    case $game_message.background
      when 1  # dim
        msgwindow.opacity=0
      when 2  # transparent
        msgwindow.opacity=0
    end
  end
end
 
def pbCreateMessageWindow(viewport = nil, skin = nil)
  arrow = nil
  if $game_temp.speechbubble_bubble == 2 && ( $game_temp.speechbubble_talking == -1 || $game_map.events[$game_temp.speechbubble_talking] != nil) 
    # Determine speaker x and y (player or event)
    if $game_temp.speechbubble_talking == -1
      speaker_x = $game_player.screen_x
      speaker_y = $game_player.screen_y
    else
      speaker = $game_map.events[$game_temp.speechbubble_talking]
      speaker_x = speaker.screen_x
      speaker_y = speaker.screen_y
    end
    
    # Create viewport for arrow
    $game_temp.speechbubble_vp = Viewport.new(0, 0, Graphics.width, Graphics.height)
    $game_temp.speechbubble_vp.z = 999999
    arrow = Sprite.new($game_temp.speechbubble_vp)
    arrow.z = 999999
    arrow.zoom_x = 2
    arrow.zoom_y = 2
    
    # Determine arrow direction and load bitmap based on event position
    if speaker_y < Graphics.height / 2
      # Event in upper half, arrow points down (bubble below event)
      arrow.bitmap = RPG::Cache.load_bitmap("Graphics/Pictures/","Arrow_Down")
      arrow.y = speaker_y - 72  # Very close to event
      $game_temp.speechbubble_arrow_direction = "down"
    else
      # Event in lower half, arrow points up (bubble above event)
      arrow.bitmap = RPG::Cache.load_bitmap("Graphics/Pictures/","Arrow_Up")
      arrow.y = speaker_y  # Very close to event
      $game_temp.speechbubble_arrow_direction = "up"
    end
    
    # Position arrow directly above or below the event (after bitmap is loaded)
    arrow.x = speaker_x - (arrow.bitmap.width * arrow.zoom_x / 2)  # Center the arrow properly on the event
    
    # Make sure arrow is visible
    arrow.opacity = 255
    arrow.visible = true
  end
  
  $game_temp.speechbubble_arrow = arrow
  msgwindow=Window_AdvancedTextPokemon.new("")
  if !viewport
    msgwindow.z=99999
  else
    msgwindow.viewport=viewport
  end
  msgwindow.visible=true
  msgwindow.letterbyletter=true
  msgwindow.back_opacity=MessageConfig::WINDOW_OPACITY
  pbBottomLeftLines(msgwindow,2)
  $game_temp.message_window_showing=true if $game_temp
  $game_message.visible=true if $game_message
  skin = "Graphics/windowskins/bubbleskinnew" if ($game_temp.speechbubble_bubble && $game_temp.speechbubble_bubble > 0) && !skin
  skin = MessageConfig.pbGetSpeechFrame() if !skin
  msgwindow.setSkin(skin)
  
  # Initialize animation variables
  $game_temp.speechbubble_animating = false
  $game_temp.speechbubble_target_y = nil
  
  return msgwindow
end

def pbDisposeMessageWindow(msgwindow)
  $game_temp.message_window_showing=false if $game_temp
  $game_message.visible=false if $game_message
  msgwindow.dispose
  $game_temp.speechbubble_arrow.dispose if $game_temp.speechbubble_arrow
  $game_temp.speechbubble_vp.dispose if $game_temp.speechbubble_vp
  # Reset animation variables
  $game_temp.speechbubble_animating = false if $game_temp
  $game_temp.speechbubble_target_y = nil if $game_temp
  $game_temp.speechbubble_arrow_direction = nil if $game_temp
end

def pbCallBub(type, value)
  if value == -1
    $game_temp.speechbubble_talking = -1
  else
    $game_temp.speechbubble_talking = get_character(value).id
  end
  $game_temp.speechbubble_bubble = type
end

def pbUpdateSpeechBubbleAnimation(msgwindow)
  return unless $game_temp.speechbubble_animating && $game_temp.speechbubble_target_y
  
  # Smooth animation constants
  animation_speed = 3
  fade_speed = 8
  
  # Animate position
  current_y = msgwindow.y
  target_y = $game_temp.speechbubble_target_y
  
  if (current_y - target_y).abs > animation_speed
    if current_y < target_y
      msgwindow.y += animation_speed
    else
      msgwindow.y -= animation_speed
    end
  else
    msgwindow.y = target_y
  end
  
  # Animate opacity
  if msgwindow.opacity < 255
    msgwindow.opacity += fade_speed
    msgwindow.opacity = 255 if msgwindow.opacity > 255
  end
  
  # Check if animation is complete
  if msgwindow.y == target_y && msgwindow.opacity >= 255
    $game_temp.speechbubble_animating = false
    # Make sure arrow is visible after bubble animation completes
    if $game_temp.speechbubble_arrow
      $game_temp.speechbubble_arrow.opacity = 255
      $game_temp.speechbubble_arrow.visible = true
    end
  end
end

def pbProcessSpeechBubbleText(text)
  return text unless $game_temp.speechbubble_bubble && $game_temp.speechbubble_bubble > 0
  
  # Split text into chunks of 45 characters max
  max_chars = 45
  if text.length <= max_chars
    return text
  end
  
  # Find appropriate break point (prefer spaces)
  break_point = text.rindex(' ', max_chars) || max_chars
  current_chunk = text[0, break_point]
  remaining_text = text[break_point..-1].strip
  
  # Store remaining text for next message
  if remaining_text.length > 0
    # You may need to implement a queue system for multiple chunks
    # For now, we'll just truncate and add ellipsis
    current_chunk += "..."
  end
  
  return current_chunk
end